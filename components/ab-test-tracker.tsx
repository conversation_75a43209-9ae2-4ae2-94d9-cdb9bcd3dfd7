"use client"

import { useEffect } from 'react'
import { useLocale } from 'next-intl'
import { setABTestCookie, trackABTestAssignment, trackABTestEvent } from '@/lib/ab-testing'

interface ABTestTrackerProps {
  testName: string
  variant: string
  isNewUser: boolean
}

export function ABTestTracker({ testName, variant, isNewUser }: ABTestTrackerProps) {
  const locale = useLocale()

  useEffect(() => {
    // 设置Cookie（确保客户端也有记录）
    setABTestCookie(testName, variant)

    // 跟踪A/B测试分配
    trackABTestAssignment(testName, variant, isNewUser, navigator.userAgent)

    // 如果是新用户分配，额外跟踪
    if (isNewUser) {
      trackABTestEvent(testName, variant, 'user_assigned', {
        locale: locale,
        referrer: document.referrer,
        landing_page: window.location.pathname
      })
    }

    // 跟踪页面浏览
    trackABTestEvent(testName, variant, 'page_view', {
      page: window.location.pathname,
      locale: locale
    })

    // 监听页面交互事件
    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      
      // 跟踪重要元素的点击
      if (target.closest('.calligraphy-generator') || 
          target.closest('[data-track="generator-interaction"]')) {
        trackABTestEvent(testName, variant, 'generator_interaction', {
          element: target.tagName.toLowerCase(),
          className: target.className
        })
      }
    }

    // 监听表单提交或下载事件
    const handleDownload = () => {
      trackABTestEvent(testName, variant, 'download_action', {
        page: window.location.pathname
      })
    }

    // 监听字体选择事件
    const handleFontSelection = () => {
      trackABTestEvent(testName, variant, 'font_selection', {
        page: window.location.pathname
      })
    }

    document.addEventListener('click', handleClick)
    
    // 监听自定义事件
    window.addEventListener('calligraphy-download', handleDownload)
    window.addEventListener('calligraphy-font-change', handleFontSelection)

    return () => {
      document.removeEventListener('click', handleClick)
      window.removeEventListener('calligraphy-download', handleDownload)
      window.removeEventListener('calligraphy-font-change', handleFontSelection)
    }
  }, [testName, variant, isNewUser, locale])

  return null // 这个组件不渲染任何UI
}

// Hook for easy A/B testing in components
export function useABTest(testName: string) {
  useEffect(() => {
    // 获取当前用户的A/B测试变体
    const getCookie = (name: string) => {
      const value = `; ${document.cookie}`
      const parts = value.split(`; ab_${name}=`)
      if (parts.length === 2) return parts.pop()?.split(';').shift()
      return null
    }

    const variant = getCookie(testName) || 'original'
    
    return {
      variant,
      track: (event: string, properties?: Record<string, any>) => {
        trackABTestEvent(testName, variant, event, properties)
      }
    }
  }, [testName])
}

// A/B测试结果分析组件（开发环境使用）
export function ABTestDebugger() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // 在控制台显示当前A/B测试状态
      const cookies = document.cookie.split(';')
      const abTests = cookies
        .filter(cookie => cookie.trim().startsWith('ab_'))
        .map(cookie => {
          const [name, value] = cookie.trim().split('=')
          return { test: name.replace('ab_', ''), variant: value }
        })

      if (abTests.length > 0) {
        console.group('🧪 A/B Test Status')
        abTests.forEach(test => {
          console.log(`${test.test}: ${test.variant}`)
        })
        console.groupEnd()
      }
    }
  }, [])

  return null
}
