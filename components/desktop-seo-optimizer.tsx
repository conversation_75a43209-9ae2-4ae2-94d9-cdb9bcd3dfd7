"use client"

import { useEffect } from 'react'
import { useTranslations } from 'next-intl'

interface DesktopSEOOptimizerProps {
  locale: string
}

export function DesktopSEOOptimizer({ locale }: DesktopSEOOptimizerProps) {
  const t = useTranslations('metadata')

  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return

    // 检测设备类型
    const isDesktop = window.innerWidth >= 1024
    const isEnglish = locale === 'en'

    // 只对英文桌面端用户优化
    if (isDesktop && isEnglish) {
      // 桌面端优化的meta描述
      const desktopDescription = "Professional Arabic Calligraphy Generator - Create Islamic Art with 13+ Premium Fonts. Free online tool for designers, students & businesses. Instant PNG/SVG download."
      
      // 更新meta描述
      const metaDescription = document.querySelector('meta[name="description"]')
      if (metaDescription) {
        metaDescription.setAttribute('content', desktopDescription)
      }

      // 更新OpenGraph描述
      const ogDescription = document.querySelector('meta[property="og:description"]')
      if (ogDescription) {
        ogDescription.setAttribute('content', desktopDescription)
      }

      // 更新Twitter描述
      const twitterDescription = document.querySelector('meta[name="twitter:description"]')
      if (twitterDescription) {
        twitterDescription.setAttribute('content', desktopDescription)
      }

      // 跟踪桌面端优化事件
      if ((window as any).trackCalligraphyEvent) {
        (window as any).trackCalligraphyEvent('Desktop_SEO_Optimization_Applied', {
          original_description: t('description'),
          optimized_description: desktopDescription,
          viewport_width: window.innerWidth,
          viewport_height: window.innerHeight,
          user_agent: navigator.userAgent,
          locale: locale,
          optimization_timestamp: new Date().toISOString()
        })
      }

      // 添加桌面端专属功能提示
      const addDesktopFeatureHints = () => {
        // 检查是否已经添加过提示
        if (document.querySelector('.desktop-feature-hints')) return

        // 创建桌面端功能提示
        const hintsContainer = document.createElement('div')
        hintsContainer.className = 'desktop-feature-hints fixed top-20 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 shadow-lg z-50 max-w-xs'
        hintsContainer.innerHTML = `
          <div class="flex items-start space-x-2">
            <div class="flex-shrink-0">
              <svg class="w-4 h-4 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <p class="font-medium">Desktop Features</p>
              <p class="mt-1">Press 1-9 to quickly select fonts</p>
              <button class="mt-2 text-xs text-blue-600 hover:text-blue-800 underline" onclick="this.parentElement.parentElement.parentElement.remove()">
                Got it
              </button>
            </div>
          </div>
        `

        // 3秒后显示提示
        setTimeout(() => {
          document.body.appendChild(hintsContainer)
          
          // 10秒后自动隐藏
          setTimeout(() => {
            if (hintsContainer.parentNode) {
              hintsContainer.remove()
            }
          }, 10000)
        }, 3000)
      }

      // 添加桌面端功能提示
      addDesktopFeatureHints()
    }

    // 监听窗口大小变化
    const handleResize = () => {
      const newIsDesktop = window.innerWidth >= 1024
      
      // 如果从移动端切换到桌面端，应用优化
      if (newIsDesktop && isEnglish && !document.querySelector('.desktop-feature-hints')) {
        // 重新应用桌面端优化
        const desktopDescription = "Professional Arabic Calligraphy Generator - Create Islamic Art with 13+ Premium Fonts. Free online tool for designers, students & businesses. Instant PNG/SVG download."
        
        const metaDescription = document.querySelector('meta[name="description"]')
        if (metaDescription) {
          metaDescription.setAttribute('content', desktopDescription)
        }
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [locale, t])

  return null // 这个组件不渲染任何UI
}

// 桌面端键盘快捷键Hook
export function useDesktopKeyboardShortcuts(onFontSelect: (fontIndex: number) => void) {
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const isDesktop = window.innerWidth >= 1024
    if (!isDesktop) return

    const handleKeyPress = (e: KeyboardEvent) => {
      // 只在没有焦点在输入框时响应
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA') {
        return
      }

      // 数字键1-9选择字体
      if (e.key >= '1' && e.key <= '9') {
        const fontIndex = parseInt(e.key) - 1
        onFontSelect(fontIndex)
        
        // 跟踪键盘快捷键使用
        if ((window as any).trackCalligraphyEvent) {
          (window as any).trackCalligraphyEvent('Desktop_Keyboard_Shortcut_Used', {
            key_pressed: e.key,
            font_index: fontIndex,
            timestamp: new Date().toISOString()
          })
        }

        e.preventDefault()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [onFontSelect])
}
