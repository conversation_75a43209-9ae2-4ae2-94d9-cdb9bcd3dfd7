// A/B测试工具库
import { cookies } from 'next/headers'

export interface ABTestConfig {
  testName: string
  variants: string[]
  trafficSplit: number[] // 例如 [50, 50] 表示各50%
}

export interface ABTestResult {
  variant: string
  isNewUser: boolean
}

// A/B测试配置
export const AB_TESTS = {
  META_DESCRIPTION: {
    testName: 'meta_description_optimization',
    variants: ['original', 'professional'],
    trafficSplit: [50, 50] // 50% original, 50% professional
  }
} as const

/**
 * 获取用户的A/B测试变体
 */
export async function getABTestVariant(config: ABTestConfig): Promise<ABTestResult> {
  const cookieStore = await cookies()
  const cookieName = `ab_${config.testName}`
  const existingVariant = cookieStore.get(cookieName)

  // 如果用户已经有分组，返回现有分组
  if (existingVariant) {
    return {
      variant: existingVariant.value,
      isNewUser: false
    }
  }

  // 新用户，随机分配
  const random = Math.random() * 100
  let cumulativeWeight = 0
  let selectedVariant = config.variants[0]

  for (let i = 0; i < config.variants.length; i++) {
    cumulativeWeight += config.trafficSplit[i]
    if (random <= cumulativeWeight) {
      selectedVariant = config.variants[i]
      break
    }
  }

  return {
    variant: selectedVariant,
    isNewUser: true
  }
}

/**
 * 设置A/B测试Cookie（在客户端组件中使用）
 */
export function setABTestCookie(testName: string, variant: string) {
  if (typeof document !== 'undefined') {
    // 设置30天过期的cookie
    const expires = new Date()
    expires.setDate(expires.getDate() + 30)
    
    document.cookie = `ab_${testName}=${variant}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`
  }
}

/**
 * 获取Meta描述的A/B测试变体
 */
export async function getMetaDescriptionVariant(locale: string): Promise<{
  description: string
  variant: string
  isNewUser: boolean
}> {
  // 只对英文用户进行A/B测试
  if (locale !== 'en') {
    return {
      description: getOriginalDescription(locale),
      variant: 'original',
      isNewUser: false
    }
  }

  const result = await getABTestVariant(AB_TESTS.META_DESCRIPTION)
  
  const description = result.variant === 'professional' 
    ? "Professional Arabic Calligraphy Generator - Create Islamic Art with 13+ Premium Fonts. Free online tool for designers, students & businesses. Instant PNG/SVG download."
    : "🎨 FREE Arabic Calligraphy Generator | Create stunning Islamic art online instantly! 13+ fonts, instant download PNG/SVG. No signup required ✨"

  return {
    description,
    variant: result.variant,
    isNewUser: result.isNewUser
  }
}

/**
 * 获取原始描述（从翻译文件）
 */
function getOriginalDescription(locale: string): string {
  // 这里应该从翻译文件获取，暂时硬编码
  const descriptions: Record<string, string> = {
    'en': "🎨 FREE Arabic Calligraphy Generator | Create stunning Islamic art online instantly! 13+ fonts, instant download PNG/SVG. No signup required ✨",
    'ar': "🎨 مولد الخط العربي المجاني | إنشاء فن إسلامي مذهل عبر الإنترنت فوراً! 13+ خط، تحميل فوري PNG/SVG. لا يتطلب تسجيل ✨",
    // 其他语言...
  }
  
  return descriptions[locale] || descriptions['en']
}

/**
 * A/B测试事件跟踪
 */
export function trackABTestEvent(
  testName: string, 
  variant: string, 
  event: string, 
  properties?: Record<string, any>
) {
  if (typeof window !== 'undefined' && (window as any).trackCalligraphyEvent) {
    (window as any).trackCalligraphyEvent('AB_Test_Event', {
      test_name: testName,
      variant: variant,
      event: event,
      timestamp: new Date().toISOString(),
      ...properties
    })
  }
}

/**
 * A/B测试分配跟踪
 */
export function trackABTestAssignment(
  testName: string, 
  variant: string, 
  isNewUser: boolean,
  userAgent?: string
) {
  if (typeof window !== 'undefined' && (window as any).trackCalligraphyEvent) {
    (window as any).trackCalligraphyEvent('AB_Test_Assignment', {
      test_name: testName,
      variant: variant,
      is_new_user: isNewUser,
      user_agent: userAgent || navigator.userAgent,
      viewport_width: window.innerWidth,
      viewport_height: window.innerHeight,
      timestamp: new Date().toISOString()
    })
  }
}
