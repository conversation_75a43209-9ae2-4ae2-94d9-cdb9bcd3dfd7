<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Impact Test</title>
    
    <!-- 服务端渲染的meta描述 -->
    <meta name="description" content="🎨 FREE Arabic Calligraphy Generator | Create stunning Islamic art online instantly! 13+ fonts, instant download PNG/SVG. No signup required ✨">
    
    <script>
        // 模拟我们的客户端修改
        document.addEventListener('DOMContentLoaded', function() {
            const isDesktop = window.innerWidth >= 1024;
            
            if (isDesktop) {
                const metaDescription = document.querySelector('meta[name="description"]');
                if (metaDescription) {
                    metaDescription.setAttribute('content', 
                        'Professional Arabic Calligraphy Generator - Create Islamic Art with 13+ Premium Fonts. Free online tool for designers, students & businesses. Instant PNG/SVG download.'
                    );
                    console.log('Meta description updated for desktop');
                }
            }
        });
    </script>
</head>
<body>
    <h1>SEO Impact Test Page</h1>
    <p>This page tests whether search engines can see client-side meta description changes.</p>
    
    <div id="current-description">
        <h2>Current Meta Description:</h2>
        <p id="description-display"></p>
    </div>
    
    <script>
        // 显示当前meta描述
        const metaDesc = document.querySelector('meta[name="description"]');
        document.getElementById('description-display').textContent = metaDesc.content;
    </script>
    
    <!-- 测试说明 -->
    <div style="margin-top: 20px; padding: 15px; background: #f0f0f0; border-radius: 5px;">
        <h3>测试方法：</h3>
        <ol>
            <li>查看页面源代码 - 搜索引擎看到的内容</li>
            <li>查看开发者工具Elements - 客户端修改后的内容</li>
            <li>使用Google Search Console的URL检查工具</li>
            <li>使用curl命令获取服务端HTML</li>
        </ol>
        
        <h3>预期结果：</h3>
        <ul>
            <li><strong>页面源代码</strong>：显示emoji版本（搜索引擎看到的）</li>
            <li><strong>Elements面板</strong>：显示专业版本（用户看到的）</li>
            <li><strong>搜索结果</strong>：仍显示emoji版本</li>
        </ul>
    </div>
</body>
</html>
